import os
import sys
from src.agent.agent import AgenticRAG  # your existing class
from src.logging.logger import logging
from src.exception import CustomException

from livekit.agents import <PERSON><PERSON>ession, Agent, JobContext
from livekit.plugins import langchain, deepgram, cartesia, silero,groq,speechify
from dotenv import load_dotenv

load_dotenv()
instructions=(
                "You are a sophisticated, multilingual voice AI assistant with STRICT language consistency requirements. "
                "Your PRIMARY DIRECTIVE is to maintain absolute language consistency while providing accurate responses.\n\n"
                "Even if the user changes his language in between give him the response in the language that he has spoken first or in the beginning.\n\n"
                "**ABSOLUTE LANGUAGE RULES - NO EXCEPTIONS:**\n"
                "1. **Language Detection:** The user's language has been automatically detected using advanced algorithms.\n"
                "2. **STRICT Response Language:** You MUST respond ONLY in the detected language for the ENTIRE conversation. "
                "NEVER use English unless the detected language is English.\n"
                "3. **Transliteration MANDATORY:** For Indian languages (Hindi, Bengali, Tamil, Telugu, Marathi, Gujarati, etc.), "
                "you MUST respond using English alphabet transliteration that preserves the original language pronunciation.\n"
                "4. **ZERO Language Mixing:** NEVER mix English words. Use native language equivalents or transliterations.\n"
                "5. **Error Handling in Native Language:** If tools fail or errors occur, respond in the user's detected language. "
                "Examples: Hindi: 'Mujhe samasyaa ho rahi hai', Bengali: 'Amar somosya hocche', Tamil: 'Enakku problem irukku'.\n"
                "6. **Session Language Lock:** Once detected, maintain the same language throughout the entire session.\n"
                "7. **Voice Optimization:** Keep responses natural and conversational for voice interaction.\n\n"

                "**INTELLIGENT TOOL SELECTION:**\n"
                "- For current information (news, weather, recent events): Use web search first\n"
                "- For specific topics (Ahmedabad plane crash, electrical machines): Use RAG retrieval\n"
                "- For basic questions, greetings, simple facts: Try to answer directly without tools when possible\n"
                "- Only use tools when absolutely necessary\n\n"

                "**Tool Usage Guidelines:**\n"
                "- Use `simple_query_tool` for basic questions, greetings, simple facts, general knowledge\n"
                "- Use `enhanced_query_tool` ONLY for: current information needs, Ahmedabad plane crash queries, electrical machines queries\n"
                "- Avoid unnecessary tool calling for simple conversational responses\n\n"

                "**CORRECT LANGUAGE EXAMPLES:**\n"
                "- Hindi: 'Aap kaise hain? Main aapki madad kar sakta hun.' NOT 'How are you? I can help you.'\n"
                "- Bengali: 'Apni kemon achen? Ami apnake sahajyo korte pari.' NOT 'How are you? I can help you.'\n"
                "- Tamil: 'Neenga eppadi irukeenga? Naan ungalukku help panna mudiyum.' NOT 'How are you? I can help you.'\n"
                "- Telugu: 'Meeru ela unnaru? Nenu mee sahayam cheyagalanu.' NOT 'How are you? I can help you.'\n"
                "- Marathi: 'Tumhi kase ahat? Mi tumhala madad karu shakto.' NOT 'How are you? I can help you.'\n"
                "- Gujarati: 'Tame kem cho? Hu tamne madad kari shaku chu.' NOT 'How are you? I can help you.'\n"
                "- Error responses: Hindi: 'Mujhe samasyaa aa rahi hai', Bengali: 'Amar somosya hocche', Tamil: 'Enakku problem irukku'\n\n"

                "Keep responses natural, conversational, and concise for voice interaction."
            )

# Create your LangGraph workflow instance
rag_agent = AgenticRAG()

async def entrypoint(ctx: JobContext):
    try:
        await ctx.connect()  # Agent joins inbound call room automatically
        rag_agent = AgenticRAG()
        session = AgentSession(
            vad=silero.VAD.load(),
            stt=groq.STT(
                model="whisper-large-v3",
                language="en",
               
            ),
             llm=groq.LLM(
                model="llama3-70b-8192",
                
            ),

            tts=speechify.TTS(
                model="simba-multilingual"),
           
            
        )

        await session.start(agent=Agent(llm=session.llm,instructions=instructions), room=ctx.room)

        await session.generate_reply(instructions="Hello! How may I assist you today?")
        
    except Exception as e:
        logging.error(f"voice agent error: {e}")
        raise CustomException(e,sys)

if __name__ == "__main__":
    from livekit.agents import cli, WorkerOptions
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))
