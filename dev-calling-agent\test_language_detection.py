#!/usr/bin/env python3
"""
Test script to verify language detection is working properly
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.language_detection import LanguageDetector

def test_language_detection():
    """Test language detection with various inputs"""
    detector = LanguageDetector()
    
    test_cases = [
        # Hindi
        ("Namaste, aap kaise hain?", "hi"),
        ("Main aapki madad kar sakta hun", "hi"),
        ("Dhanyawad", "hi"),
        
        # Tamil  
        ("Vanakkam, eppadi irukkenga?", "ta"),
        ("Naan ungalukku uthavi seiya mudiyum", "ta"),
        ("Nandri", "ta"),
        
        # Telugu
        ("Namaskaram, ela unnaru?", "te"),
        ("Nenu mee sahayam cheyagalanu", "te"),
        ("Dhanyawadalu", "te"),
        
        # German
        ("<PERSON><PERSON>, wie geht es <PERSON>en?", "de"),
        ("<PERSON>ch kann <PERSON>en helfen", "de"),
        ("Dan<PERSON>", "de"),
        
        # French
        ("Bonjour, comment allez-vous?", "fr"),
        ("Je peux vous aider", "fr"),
        ("Merci", "fr"),
        
        # English
        ("Hello, how are you?", "en"),
        ("I can help you", "en"),
        ("Thank you", "en"),
    ]
    
    print("🔍 Testing Language Detection:")
    print("=" * 50)
    
    for text, expected_lang in test_cases:
        lang_code, lang_name, lang_config = detector.detect_language(text)
        
        status = "✅ PASS" if lang_code == expected_lang else "❌ FAIL"
        print(f"{status} | Input: '{text}'")
        print(f"      | Expected: {expected_lang} | Detected: {lang_code} ({lang_name})")
        
        if lang_config and 'sample_phrases' in lang_config:
            greeting = lang_config['sample_phrases'].get('greeting', 'N/A')
            print(f"      | Sample greeting: '{greeting}'")
        
        print("-" * 50)

if __name__ == "__main__":
    test_language_detection()
