import os
import sys
import asyncio
from typing import Dict, Any, Optional
from src.agent.agent import AgenticRA<PERSON>
from src.logging.logger import logging
from src.exception import CustomException
from src.utils.language_detection import LanguageDetector
from src.tools.vector_database_tool import VectorDatabaseTool
from src.tools.web_search_tool import WebSearchTool
from livekit.plugins.turn_detector.multilingual import MultilingualModel
from livekit.agents import AgentSession, Agent, JobContext, llm
from livekit.plugins import silero, groq, speechify
from dotenv import load_dotenv

load_dotenv()

# Global variables for session language tracking
session_language = None
session_language_config = None
rag_agent = AgenticRAG()
language_detector = LanguageDetector()

def get_language_specific_instructions(lang_code: str, lang_config: dict) -> str:
    """Generate language-specific instructions for the agent."""
    if lang_code == 'en':
        return """
You are a helpful voice AI assistant. Respond in clear, natural English.

CRITICAL RULES:
- For greetings (hello, hi, how are you): Respond directly WITHOUT using tools
- For technical questions: Use vector_database_search
- For current events/news/weather: Use web_search
- Keep responses conversational and concise for voice interaction

TOOLS AVAILABLE:
- vector_database_search: For technical documentation and internal knowledge
- web_search: For current news and real-time information
"""

    lang_name = lang_config.get('name', 'Unknown')
    sample_greeting = lang_config.get('sample_phrases', {}).get('greeting', '')
    sample_error = lang_config.get('sample_phrases', {}).get('error', '')

    return f"""
You are a helpful multilingual voice AI assistant.

🚨 ABSOLUTE LANGUAGE RULE 🚨
- You MUST respond ONLY in {lang_name} using English alphabet
- NEVER use English words in your responses
- Example greeting: '{sample_greeting}'
- Example error: '{sample_error}'

CRITICAL RESPONSE RULES:
- For greetings (namaste, vanakkam, hello): Respond directly in {lang_name} WITHOUT using tools
- For technical questions: Use vector_database_search
- For current events/news/weather: Use web_search
- Always respond in {lang_name} using English alphabet transliteration

TOOLS AVAILABLE:
- vector_database_search: For technical documentation and internal knowledge
- web_search: For current news and real-time information

EXAMPLES:
- User says "Namaste" → You respond: "{sample_greeting}" (NO TOOLS NEEDED)
- User asks technical question → Use vector_database_search
- User asks about weather → Use web_search
"""

def create_vector_database_tool():
    """Create a vector database search tool."""
    vector_tool = VectorDatabaseTool(rag_agent.llm)

    @llm.function_tool(
        name="vector_database_search",
        description="Search technical documentation ONLY. Use for: motor specs, transformer details, electrical machines, technical manuals. NEVER use for: greetings, weather, news, simple chat."
    )
    async def vector_database_search(query: str) -> str:
        """Search internal vector database for technical documentation."""
        try:
            global session_language, session_language_config
            logging.info(f"Vector database search: '{query}'")

            # Detect language from query if not already set
            if session_language is None:
                lang_code, lang_name, lang_config = language_detector.detect_language(query)
                session_language = lang_code
                session_language_config = lang_config
                logging.info(f"Language detected from query: {lang_name} ({lang_code})")

            # Search vector database
            result = vector_tool.search_documents(query, session_language or 'en')

            if result['is_relevant']:
                response = result['results']
                logging.info("Vector database found relevant results")
            else:
                # No relevant results message in user's language
                if session_language == 'hi':
                    response = "Mere paas is technical sawal ka jawaab nahi hai. Kya main web search kar sakta hun?"
                elif session_language == 'ta':
                    response = "Enakku indha technical kelvikku information illa. Web search pannattuma?"
                elif session_language == 'te':
                    response = "Naa daggara ee technical prashnaku information ledu. Web search cheyamantaara?"
                elif session_language == 'de':
                    response = "Ich habe keine technischen Informationen dazu. Soll ich im Web suchen?"
                elif session_language == 'fr':
                    response = "Je n'ai pas d'informations techniques à ce sujet. Dois-je chercher sur le web?"
                else:
                    response = "I don't have technical information about this. Should I search the web?"
                logging.info("Vector database did not find relevant results")

            return response

        except Exception as e:
            logging.error(f"Vector database tool error: {e}")
            if session_language == 'hi':
                return "Knowledge base mein kuch technical problem hai."
            elif session_language == 'ta':
                return "Knowledge base la konjam technical problem irukku."
            elif session_language == 'te':
                return "Knowledge base lo konni technical samasyalu unnaayi."
            elif session_language == 'de':
                return "Es gibt ein technisches Problem mit der Wissensdatenbank."
            elif session_language == 'fr':
                return "Il y a un problème technique avec la base de connaissances."
            else:
                return "Technical issue with knowledge base."

    return vector_database_search

def create_web_search_tool():
    """Create a web search tool."""
    web_tool = WebSearchTool()

    @llm.function_tool(
        name="web_search",
        description="Search internet for current news, weather, real-time info ONLY. Use for: today's news, weather updates, recent events. NEVER use for: greetings, technical docs, simple chat."
    )
    async def web_search(query: str) -> str:
        """Search the web for current information."""
        try:
            global session_language, session_language_config
            logging.info(f"Web search: '{query}'")

            # Detect language from query if not already set
            if session_language is None:
                lang_code, lang_name, lang_config = language_detector.detect_language(query)
                session_language = lang_code
                session_language_config = lang_config
                logging.info(f"Language detected from query: {lang_name} ({lang_code})")

            # Perform web search
            result = web_tool.search_web(query, session_language or 'en')
            response = result['results']
            logging.info(f"Web search completed with {result['result_count']} results")

            return response

        except Exception as e:
            logging.error(f"Web search tool error: {e}")
            # Return error message in user's language
            if session_language == 'hi':
                return "Web search mein technical problem hai."
            elif session_language == 'ta':
                return "Web search la technical problem irukku."
            elif session_language == 'te':
                return "Web search lo technical problem undi."
            elif session_language == 'de':
                return "Technisches Problem bei der Websuche."
            elif session_language == 'fr':
                return "Problème technique avec la recherche web."
            else:
                return "Technical issue with web search."

    return web_search

class LanguageAwareAgent(Agent):
    """Enhanced Agent with language detection and enforcement."""

    def __init__(self, llm, tools):
        # Simple, fast instructions for better performance
        initial_instructions = """Multilingual voice AI assistant.

LANGUAGE RULE: Respond in user's language using English alphabet.
- Hindi → Hindi (English letters): "Namaste, main madad kar sakta hun"
- Tamil → Tamil (English letters): "Vanakkam, naan uthavi seiya mudiyum"
- Telugu → Telugu (English letters): "Namaskaram, nenu sahayam cheyagalanu"
- German → German: "Hallo, ich kann helfen"
- French → French: "Bonjour, je peux aider"
- English → English: "Hello, I can help"

TOOLS:
- Greeting → NO TOOLS
- Technical → vector_database_search
- News/weather → web_search"""

        super().__init__(
            llm=llm,
            instructions=initial_instructions,
            tools=tools
        )
        self.current_language = None
        self.language_config = None

    async def _handle_user_message(self, message):
        """Override to detect language and update instructions."""
        global session_language, session_language_config

        # Only detect language if not already set (prevent switching)
        if message.content and session_language is None:
            lang_code, lang_name, lang_config = language_detector.detect_language(message.content)
            logging.info(f"Initial language detection: '{message.content}' → {lang_name} ({lang_code})")

            # Set session language only once
            if lang_code != 'unknown':
                session_language = lang_code
                session_language_config = lang_config
                self.current_language = lang_code
                self.language_config = lang_config

                logging.info(f"Session language LOCKED to: {lang_name} ({lang_code})")
        elif session_language:
            # Use existing session language
            self.current_language = session_language
            self.language_config = session_language_config
            logging.info(f"Using locked session language: {session_language}")

        # Update instructions only if language changed
        if session_language == 'en':
            self.instructions = """You are a helpful voice AI assistant.
RESPOND IN ENGLISH ONLY.

TOOL RULES:
- Greeting (hello, hi) → NO TOOLS, respond directly
- Technical/documentation questions → USE vector_database_search
- Current news/events → USE web_search
- Weather/real-time info → USE web_search

Be concise and direct."""
        else:
            lang_name = session_language_config.get('name', 'Unknown')
            sample_greeting = session_language_config.get('sample_phrases', {}).get('greeting', '')

            self.instructions = f"""CRITICAL: RESPOND ONLY IN {lang_name.upper()} using English alphabet.

LANGUAGE RULE: {lang_name} ONLY - NO ENGLISH WORDS
Example: "{sample_greeting}"

TOOL RULES:
- Greeting → NO TOOLS, respond in {lang_name}
- Technical questions → USE vector_database_search
- News/current events → USE web_search

MANDATORY: Every word in {lang_name} using English letters."""

        return await super()._handle_user_message(message)

async def entrypoint(ctx: JobContext):
    """Main entrypoint for the voice agent."""
    global session_language, session_language_config
    
    try:
        await ctx.connect()
        
        # Reset session language for new session
        session_language = None
        session_language_config = None
        
        # Create tools
        vector_tool = create_vector_database_tool()
        web_tool = create_web_search_tool()
        
        # Create session with language detection enabled
        session = AgentSession(
            vad=silero.VAD.load(),
            stt=groq.STT(
                model="whisper-large-v3",
                detect_language=True,
              # Auto-detect language
            ),
            llm=groq.LLM(
                model="llama3-70b-8192",
                temperature=0.1,  # Very low temperature for fast, consistent responses
            ),
            tts=speechify.TTS(
                model="simba-multilingual",
                
            ),
        )
        
        # Create language-aware agent
        agent = LanguageAwareAgent(
            llm=session.llm,
            tools=[vector_tool, web_tool]
        )
        
        await session.start(agent=agent, room=ctx.room)
        
        # Start with simple multilingual greeting
        await session.generate_reply(
            instructions="""Say a brief multilingual greeting: 'Hello! Namaste! Vanakkam! How can I help you today?'
Keep it short and wait for user response to detect their language."""
        )
        
    except Exception as e:
        logging.error(f"Voice agent error: {e}")
        raise CustomException(e, sys)

if __name__ == "__main__":
    from livekit.agents import cli, WorkerOptions
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))
