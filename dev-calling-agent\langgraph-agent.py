import os
import sys
import asyncio
from typing import Dict, List, Any, Optional
from src.agent.agent import AgenticRAG  # your existing class
from src.logging.logger import logging
from src.exception import CustomException
from src.utils.language_detection import LanguageDetector
from src.tools.vector_database_tool import VectorDatabaseTool
from src.tools.web_search_tool import WebSearchTool

from livekit.agents import <PERSON>Session, Agent, JobContext, llm
from livekit.plugins import langchain, deepgram, cartesia, silero,groq,speechify
from dotenv import load_dotenv

load_dotenv()
# Base instructions that will be dynamically updated
base_instructions = (
    "You are a sophisticated, multilingual voice AI assistant with advanced language detection and intelligent tool routing capabilities.\n\n"

    "**INTELLIGENT TOOL ROUTING - CRITICAL RULES:**\n"
    "You have access to TWO distinct tools:\n\n"

    "🔍 **vector_database_search** - Use for:\n"
    "- Technical documentation (electrical machines, transformers, motors)\n"
    "- Internal knowledge and stored documents\n"
    "- Domain-specific information\n"
    "- Company policies and procedures\n"
    "- Historical data and specifications\n\n"

    "🌐 **web_search** - Use for:\n"
    "- Current news and recent events\n"
    "- Real-time information (weather, stock prices)\n"
    "- Breaking news and latest developments\n"
    "- Recent product releases\n"
    "- When vector database doesn't have relevant information\n\n"

    "**TOOL SELECTION PRIORITY:**\n"
    "1. For technical/domain questions → Try vector_database_search FIRST\n"
    "2. For current events/real-time data → Use web_search DIRECTLY\n"
    "3. If vector search returns insufficient results → Use web_search as fallback\n"
    "4. For simple greetings/basic conversation → Respond directly without tools\n\n"
)

def get_dynamic_instructions(lang_code: str = None, lang_config: dict = None) -> str:
    """
    Generate dynamic instructions based on detected language.
    """
    if lang_code is None or lang_code == 'en':
        language_rules = (
            "🚨 **LANGUAGE RULE:**\n"
            "- Respond in clear, natural English\n"
            "- Keep responses conversational and concise for voice interaction\n\n"
        )
        examples = (
            "🎯 **RESPONSE EXAMPLES:**\n"
            "User says 'Hello, can you help?' → You respond: 'Hello, I can help you with that'\n"
            "User asks about weather → You respond: 'Let me check the current weather for you'\n\n"
        )
    else:
        lang_name = lang_config.get('name', 'Unknown') if lang_config else 'Unknown'
        sample_greeting = lang_config.get('sample_phrases', {}).get('greeting', '') if lang_config else ''

        language_rules = (
            f"🚨 **ABSOLUTE MANDATORY LANGUAGE RULE - NO EXCEPTIONS:**\n"
            f"- **NEVER RESPOND IN ENGLISH** - You MUST respond ONLY in {lang_name}\n"
            f"- **USE ENGLISH ALPHABET TRANSLITERATION** for {lang_name} words\n"
            f"- **ZERO ENGLISH MIXING** - Never use English words in your responses\n"
            f"- **MAINTAIN CONSISTENCY** - Same language throughout entire conversation\n"
            f"- **EXAMPLE FORMAT:** '{sample_greeting}'\n\n"
        )

        if lang_code == 'hi':
            examples = (
                "🎯 **MANDATORY RESPONSE EXAMPLES - FOLLOW EXACTLY:**\n"
                "User says 'Namaste, madad kar sakte hain?' → You respond: 'Namaste, main aapki madad kar sakta hun'\n"
                "User asks about weather → You respond: 'Main aapke liye mausam ki jaankaari dekh raha hun'\n"
                "User says 'Dhanyawad' → You respond: 'Koi baat nahi, main yahan aapki madad ke liye hun'\n\n"
            )
        elif lang_code == 'ta':
            examples = (
                "� **MANDATORY RESPONSE EXAMPLES - FOLLOW EXACTLY:**\n"
                "User says 'Vanakkam, help panna mudiyuma?' → You respond: 'Vanakkam, naan ungalukku uthavi seiya mudiyum'\n"
                "User asks about weather → You respond: 'Naan ungalukku weather information paakka poren'\n"
                "User says 'Nandri' → You respond: 'Paravala, naan inga ungalukku uthavi seiya irukken'\n\n"
            )
        elif lang_code == 'te':
            examples = (
                "🎯 **MANDATORY RESPONSE EXAMPLES - FOLLOW EXACTLY:**\n"
                "User says 'Namaskaram, sahayam cheyagalara?' → You respond: 'Namaskaram, nenu mee sahayam cheyagalanu'\n"
                "User asks about weather → You respond: 'Nenu meeku weather information chustha'\n"
                "User says 'Dhanyawadalu' → You respond: 'Parledu, nenu ikkada mee sahayam kosam unnaanu'\n\n"
            )
        elif lang_code == 'de':
            examples = (
                "🎯 **MANDATORY RESPONSE EXAMPLES - FOLLOW EXACTLY:**\n"
                "User says 'Hallo, können Sie helfen?' → You respond: 'Hallo, ich kann Ihnen helfen'\n"
                "User asks about weather → You respond: 'Ich schaue das Wetter für Sie nach'\n"
                "User says 'Danke' → You respond: 'Gern geschehen, ich bin hier um Ihnen zu helfen'\n\n"
            )
        elif lang_code == 'fr':
            examples = (
                "🎯 **MANDATORY RESPONSE EXAMPLES - FOLLOW EXACTLY:**\n"
                "User says 'Bonjour, pouvez-vous aider?' → You respond: 'Bonjour, je peux vous aider'\n"
                "User asks about weather → You respond: 'Je vérifie la météo pour vous'\n"
                "User says 'Merci' → You respond: 'De rien, je suis là pour vous aider'\n\n"
            )
        else:
            examples = (
                f"🎯 **RESPONSE EXAMPLES for {lang_name}:**\n"
                f"Always respond in {lang_name} using appropriate transliteration\n"
                f"Example greeting: '{sample_greeting}'\n\n"
            )

    critical_rules = (
        "🚨 **CRITICAL RESPONSE RULES:**\n"
        "1. **IMMEDIATE LANGUAGE DETECTION:** Detect user's language from their first message\n"
        "2. **CONSISTENT LANGUAGE USE:** Use the same language throughout the entire conversation\n"
        "3. **TOOL RESPONSE FORMATTING:** When using tools, format your final response in the user's language\n"
        "4. **VOICE OPTIMIZATION:** Make responses sound natural when spoken aloud\n"
        "5. **LANGUAGE CONTEXT PROCESSING:** When tools provide '[LANGUAGE: X]' or '[MANDATORY LANGUAGE: X]' markers, follow those instructions exactly\n"
    )

    return base_instructions + language_rules + examples + critical_rules
            

# Create your LangGraph workflow instance and tools
rag_agent = AgenticRAG()
language_detector = LanguageDetector()

# Global variables for session language tracking
session_language = None
session_language_config = None
session_language_instructions = None
current_agent = None  # Reference to the current agent for instruction updates

class ParallelToolExecutor:
    """
    Handles parallel execution of multiple tools to reduce latency.
    """
    def __init__(self, vector_tool, web_tool):
        self.vector_tool = vector_tool
        self.web_tool = web_tool

    async def execute_parallel_search(self, query: str, use_vector: bool = True, use_web: bool = True) -> Dict[str, Any]:
        """
        Execute vector and web search in parallel for faster results.
        """
        tasks = []

        if use_vector:
            tasks.append(self._execute_vector_search(query))
        if use_web:
            tasks.append(self._execute_web_search(query))

        if not tasks:
            return {"error": "No tools selected for execution"}

        # Execute all tasks in parallel
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results
        combined_result = {
            "vector_result": None,
            "web_result": None,
            "combined_response": "",
            "language_enforcement": "",
            "execution_time": "parallel"
        }

        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logging.error(f"Tool execution error: {result}")
                continue

            if use_vector and i == 0:
                combined_result["vector_result"] = result
            elif use_web and (i == 1 or (not use_vector and i == 0)):
                combined_result["web_result"] = result

        # Combine results with enhanced language enforcement
        combined_result["combined_response"] = self._combine_results(
            combined_result["vector_result"],
            combined_result["web_result"]
        )

        return combined_result

    async def _execute_vector_search(self, query: str) -> Dict[str, Any]:
        """Execute vector database search."""
        try:
            global session_language, session_language_config

            # Detect language if not set
            if session_language is None:
                lang_code, lang_name, lang_config = language_detector.detect_language(query)
                session_language = lang_code
                session_language_config = lang_config
                logging.info(f"Language detected in vector search: {lang_name} ({lang_code})")

            # Initialize vector tool
            vector_db_tool = VectorDatabaseTool(rag_agent.llm)
            result = vector_db_tool.search_documents(query, session_language)

            return {
                "source": "vector",
                "is_relevant": result.get('is_relevant', False),
                "content": result.get('results', ''),
                "language": session_language
            }
        except Exception as e:
            logging.error(f"Vector search error: {e}")
            return {"source": "vector", "error": str(e), "is_relevant": False}

    async def _execute_web_search(self, query: str) -> Dict[str, Any]:
        """Execute web search."""
        try:
            global session_language, session_language_config

            # Detect language if not set
            if session_language is None:
                lang_code, lang_name, lang_config = language_detector.detect_language(query)
                session_language = lang_code
                session_language_config = lang_config
                logging.info(f"Language detected in web search: {lang_name} ({lang_code})")

            # Initialize web tool
            web_search_tool = WebSearchTool()
            result = web_search_tool.search_web(query, session_language)

            return {
                "source": "web",
                "content": result.get('results', ''),
                "result_count": result.get('result_count', 0),
                "language": session_language
            }
        except Exception as e:
            logging.error(f"Web search error: {e}")
            return {"source": "web", "error": str(e), "result_count": 0}

    def _combine_results(self, vector_result: Optional[Dict], web_result: Optional[Dict]) -> str:
        """
        Combine results from multiple tools with enhanced language enforcement.
        """
        global session_language, session_language_config

        combined_content = []

        # Add vector results if available and relevant
        if vector_result and vector_result.get('is_relevant', False):
            combined_content.append(f"📚 Internal Knowledge:\n{vector_result['content']}")

        # Add web results if available
        if web_result and web_result.get('result_count', 0) > 0:
            combined_content.append(f"🌐 Current Information:\n{web_result['content']}")

        # If no results from either tool
        if not combined_content:
            if session_language == 'hi':
                return "Maaf kijiye, mujhe is sawal ka jawaab nahi mil raha. Kripaya apna sawal dusre tarike se puchiye."
            elif session_language == 'ta':
                return "Mannikkavum, enakku indha kelvikku badhil kidaikala. Ungaloda kelviya vera vidhamaaga kekka mudiyuma?"
            elif session_language == 'te':
                return "Kshaminchandi, nenu ee prashnaku samadhanam dorakaledhu. Dayachesi mee prashnanu vere vidhamgaa adagandi."
            elif session_language == 'de':
                return "Entschuldigung, ich konnte keine Antwort auf Ihre Frage finden. Bitte formulieren Sie Ihre Frage anders."
            elif session_language == 'fr':
                return "Désolé, je n'ai pas pu trouver de réponse à votre question. Veuillez reformuler votre question."
            else:
                return "I couldn't find relevant information for your query. Please try rephrasing your question."

        # Combine all content
        final_response = "\n\n".join(combined_content)

        # Add critical language enforcement
        if session_language != 'en' and session_language_config:
            lang_name = session_language_config['name']
            sample_greeting = session_language_config['sample_phrases']['greeting']
            sample_error = session_language_config['sample_phrases']['error']

            enforcement_header = f"""[🚨 ABSOLUTE LANGUAGE ENFORCEMENT - CRITICAL 🚨]
MANDATORY RESPONSE LANGUAGE: {lang_name} ({session_language})
ZERO TOLERANCE RULE: NEVER USE ENGLISH WORDS - ONLY {lang_name.upper()}
TRANSLITERATION REQUIRED: Use English alphabet for {lang_name} pronunciation
EXAMPLES TO FOLLOW:
- Greeting style: '{sample_greeting}'
- Error style: '{sample_error}'
VIOLATION CONSEQUENCE: Response will be completely rejected if English is detected
[END CRITICAL ENFORCEMENT]

"""
            final_response = enforcement_header + final_response

        return final_response

def create_intelligent_search_tool():
    """
    Create an intelligent search tool that can execute vector and web search in parallel or individually.
    """
    # Initialize tools
    vector_db_tool = VectorDatabaseTool(rag_agent.llm)
    web_search_tool = WebSearchTool()
    parallel_executor = ParallelToolExecutor(vector_db_tool, web_search_tool)

    @llm.function_tool(
        name="intelligent_search",
        description="Intelligent search that automatically determines whether to use vector database, web search, or both in parallel for optimal results and speed"
    )
    async def intelligent_search(query: str, search_type: str = "auto") -> str:
        """
        Intelligent search that optimizes for both relevance and speed.

        Args:
            query: The user's question or search query
            search_type: "auto" (default), "vector_only", "web_only", or "parallel"

        Returns:
            Comprehensive response from the most appropriate search method(s)
        """
        try:
            global session_language, session_language_config, session_language_instructions, current_agent
            logging.info(f"Intelligent search invoked with query: '{query}', type: '{search_type}'")

            # Detect language if not already set for session
            if session_language is None:
                lang_code, lang_name, lang_config = language_detector.detect_language(query)
                session_language = lang_code
                session_language_config = lang_config
                session_language_instructions = language_detector.get_transliteration_instructions(lang_code)
                logging.info(f"Language detected: {lang_name} ({lang_code})")

                # Update agent instructions if agent reference is available
                if current_agent and hasattr(current_agent, 'update_language_instructions'):
                    current_agent.update_language_instructions(lang_code, lang_config)

            # Determine search strategy
            if search_type == "auto":
                search_strategy = _determine_search_strategy(query)
            else:
                search_strategy = search_type

            logging.info(f"Using search strategy: {search_strategy}")

            # Execute search based on strategy
            if search_strategy == "parallel":
                result = await parallel_executor.execute_parallel_search(query, use_vector=True, use_web=True)
                response = result["combined_response"]
                logging.info("Parallel search completed")

            elif search_strategy == "vector_only":
                vector_result = await parallel_executor._execute_vector_search(query)
                if vector_result.get('is_relevant', False):
                    response = vector_result['content']
                else:
                    # Fallback to web search if vector has no relevant results
                    logging.info("Vector search not relevant, falling back to web search")
                    web_result = await parallel_executor._execute_web_search(query)
                    response = web_result.get('content', 'No relevant information found.')

            elif search_strategy == "web_only":
                web_result = await parallel_executor._execute_web_search(query)
                response = web_result.get('content', 'No current information found.')

            else:  # Default to parallel
                result = await parallel_executor.execute_parallel_search(query, use_vector=True, use_web=True)
                response = result["combined_response"]

            # Apply enhanced language enforcement
            response = _apply_enhanced_language_enforcement(response)

            return response

        except Exception as e:
            logging.error(f"Intelligent search tool error: {e}")
            return _get_error_message_in_language()

    def _determine_search_strategy(query: str) -> str:
        """
        Determine the optimal search strategy based on query content.
        """
        query_lower = query.lower()

        # Keywords that suggest current/real-time information
        current_keywords = [
            'current', 'latest', 'recent', 'today', 'now', 'breaking', 'news',
            'weather', 'stock', 'price', 'market', 'live', 'update', 'happening'
        ]

        # Keywords that suggest technical/internal information
        technical_keywords = [
            'specification', 'manual', 'documentation', 'procedure', 'policy',
            'electrical', 'motor', 'transformer', 'generator', 'machine', 'technical'
        ]

        # Check for current information indicators
        current_score = sum(1 for keyword in current_keywords if keyword in query_lower)
        technical_score = sum(1 for keyword in technical_keywords if keyword in query_lower)

        # Decision logic
        if current_score > 0 and technical_score == 0:
            return "web_only"
        elif technical_score > 0 and current_score == 0:
            return "vector_only"
        elif len(query.split()) > 10:  # Complex queries benefit from parallel search
            return "parallel"
        else:
            return "parallel"  # Default to parallel for comprehensive results

    def _apply_enhanced_language_enforcement(response: str) -> str:
        """
        Apply the strongest possible language enforcement to the response.
        """
        global session_language, session_language_config

        if session_language != 'en' and session_language_config:
            lang_name = session_language_config['name']
            sample_greeting = session_language_config['sample_phrases']['greeting']
            sample_error = session_language_config['sample_phrases']['error']

            # Create the most aggressive language enforcement possible
            super_enforcement = f"""[🔥 SUPER CRITICAL LANGUAGE ENFORCEMENT 🔥]
⚠️  ABSOLUTE MANDATORY RULE - NO EXCEPTIONS WHATSOEVER ⚠️
REQUIRED LANGUAGE: {lang_name} ({session_language}) ONLY
🚫 ENGLISH WORDS COMPLETELY FORBIDDEN 🚫
✅ TRANSLITERATION MANDATORY: English alphabet for {lang_name} sounds
📝 FOLLOW THESE EXACT PATTERNS:
   • Greeting pattern: '{sample_greeting}'
   • Error pattern: '{sample_error}'
🎯 YOUR RESPONSE MUST SOUND LIKE A NATIVE {lang_name} SPEAKER
💀 CRITICAL WARNING: Any English word usage will result in complete response rejection
[END SUPER ENFORCEMENT - COMPLY IMMEDIATELY]

{response}

[🔄 REMINDER: Respond in {lang_name} using English alphabet transliteration ONLY]"""

            return super_enforcement
        else:
            return f"[LANGUAGE: English - Respond in natural English]\n\n{response}"

    def _get_error_message_in_language() -> str:
        """Get error message in the user's detected language."""
        global session_language

        if session_language == 'hi':
            return "Maaf kijiye, mujhe search karne mein kuch dikkat ho rahi hai. Kripaya thoda intezaar karke phir try kijiye."
        elif session_language == 'ta':
            return "Mannikkavum, enakku search pannuradhu la konjam problem irukku. Konjam wait pannitu maadhubaadhum try pannunga."
        elif session_language == 'te':
            return "Kshaminchandi, naaku search cheyadam lo konni samasyalu unnaayi. Konni sepatlu wait chesi malli try cheyandi."
        elif session_language == 'de':
            return "Entschuldigung, ich habe ein Problem beim Suchen. Bitte warten Sie einen Moment und versuchen Sie es erneut."
        elif session_language == 'fr':
            return "Désolé, j'ai un problème lors de la recherche. Veuillez attendre un moment et réessayer."
        else:
            return "I encountered an issue during search. Please wait a moment and try again."

    return intelligent_search

def create_vector_database_tool():
    """
    Create a vector database search tool for internal knowledge retrieval.
    """
    # Initialize the vector database tool
    vector_tool = VectorDatabaseTool(rag_agent.llm)

    @llm.function_tool(
        name="vector_database_search",
        description="Search internal documents and knowledge base for technical information, specifications, and stored content"
    )
    async def vector_database_search(query: str) -> str:
        """
        Search internal vector database for technical documentation and stored knowledge.

        Use this tool for:
        - Technical questions (electrical machines, motors, generators, transformers)
        - Internal documentation and manuals
        - Domain-specific information
        - Company policies and procedures
        - Historical data and archived content
        - Previously stored research and documents

        Args:
            query: The user's question about internal knowledge or technical information

        Returns:
            Response from internal knowledge base
        """
        try:
            global session_language, session_language_config, session_language_instructions, current_agent
            logging.info(f"Vector database tool invoked with query: '{query}'")

            # Detect language if not already set for session
            if session_language is None:
                lang_code, lang_name, lang_config = language_detector.detect_language(query)
                session_language = lang_code
                session_language_config = lang_config
                session_language_instructions = language_detector.get_transliteration_instructions(lang_code)
                logging.info(f"Language detected: {lang_name} ({lang_code})")
                logging.info(f"Language type: {language_detector.get_language_type(lang_code)}")

                # Update agent instructions if agent reference is available
                if current_agent and hasattr(current_agent, 'update_language_instructions'):
                    current_agent.update_language_instructions(lang_code, lang_config)

            # Search vector database with language context
            result = vector_tool.search_documents(query, session_language)

            if result['is_relevant']:
                response = result['results']
                logging.info(f"Vector database found relevant results")

                # CRITICAL: Add mandatory language instruction for LLM
                if session_language != 'en':
                    lang_name = session_language_config['name']
                    sample_greeting = session_language_config['sample_phrases']['greeting']
                    sample_error = session_language_config['sample_phrases']['error']
                    response = f"""[🚨 CRITICAL LANGUAGE ENFORCEMENT 🚨]
MANDATORY LANGUAGE: {lang_name} ({session_language})
ABSOLUTE RULE: RESPOND ONLY IN {lang_name.upper()} - NEVER USE ENGLISH WORDS
TRANSLITERATION: Use English alphabet for {lang_name} words
EXAMPLES:
- Greeting: '{sample_greeting}'
- Error: '{sample_error}'
VIOLATION PENALTY: Response will be rejected if English is used
[END LANGUAGE ENFORCEMENT]

{response}"""
                else:
                    response = f"[LANGUAGE: English - Respond in natural English]\n\n{response}"
            else:
                # Provide language-appropriate "no results" message
                if session_language == 'hi':
                    response = "Mere paas is sawal ka jawaab mere knowledge base mein nahi hai. Kripaya web search try kariye current information ke liye."
                elif session_language == 'ta':
                    response = "Enakku indha kelvikku ennoda knowledge base la relevant information illa. Current information kkaaga web search try pannunga."
                elif session_language == 'te':
                    response = "Naa knowledge base lo ee prashnaku sambandhinchinavi levu. Current information kosam web search try cheyandi."
                elif session_language == 'de':
                    response = "Ich habe keine relevanten Informationen in meiner Wissensdatenbank für diese Anfrage. Bitte versuchen Sie die Websuche für aktuelle Informationen."
                elif session_language == 'fr':
                    response = "Je n'ai pas d'informations pertinentes dans ma base de connaissances pour cette requête. Veuillez essayer la recherche web pour des informations actuelles."
                else:
                    response = "I don't have relevant information in my knowledge base for this query. Please try the web search for current information."
                logging.info(f"Vector database did not find relevant results")

            return response

        except Exception as e:
            logging.error(f"Vector database tool error: {e}")
            return "I encountered an issue searching the knowledge base. Please try rephrasing your question."

    return vector_database_search

def create_web_search_tool():
    """
    Create a web search tool for current and real-time information.
    """
    # Initialize the web search tool
    web_tool = WebSearchTool()

    @llm.function_tool(
        name="web_search",
        description="Search the internet for current news, real-time information, and recent events"
    )
    async def web_search(query: str) -> str:
        """
        Search the web for current information, news, and real-time data.

        Use this tool for:
        - Current news and recent events
        - Real-time information (weather, stock prices)
        - Breaking news and latest developments
        - Recent product releases or announcements
        - Current market data and live information
        - When vector database doesn't have relevant information

        Args:
            query: The user's question about current events or real-time information

        Returns:
            Current information from web search
        """
        try:
            global session_language, session_language_config, session_language_instructions, current_agent
            logging.info(f"Web search tool invoked with query: '{query}'")

            # Detect language if not already set for session
            if session_language is None:
                lang_code, lang_name, lang_config = language_detector.detect_language(query)
                session_language = lang_code
                session_language_config = lang_config
                session_language_instructions = language_detector.get_transliteration_instructions(lang_code)
                logging.info(f"Language detected: {lang_name} ({lang_code})")

                # Update agent instructions if agent reference is available
                if current_agent and hasattr(current_agent, 'update_language_instructions'):
                    current_agent.update_language_instructions(lang_code, lang_config)

            # Use session language for web search
            result = web_tool.search_web(query, session_language)

            response = result['results']
            logging.info(f"Web search completed with {result['result_count']} results")

            # CRITICAL: Add mandatory language instruction for LLM
            if session_language != 'en':
                lang_name = session_language_config['name']
                sample_greeting = session_language_config['sample_phrases']['greeting']
                sample_error = session_language_config['sample_phrases']['error']
                response = f"""[ CRITICAL LANGUAGE ENFORCEMENT ]
MANDATORY LANGUAGE: {lang_name} ({session_language})
ABSOLUTE RULE: RESPOND ONLY IN {lang_name.upper()} - NEVER USE ENGLISH WORDS
TRANSLITERATION: Use English alphabet for {lang_name} words
EXAMPLES:
- Greeting: '{sample_greeting}'
- Error: '{sample_error}'
VIOLATION PENALTY: Response will be rejected if English is used
[END LANGUAGE ENFORCEMENT]

{response}"""
            else:
                response = f"[LANGUAGE: English - Respond in natural English]\n\n{response}"

            return response

        except Exception as e:
            logging.error(f"Web search tool error: {e}")
            # Return error message in user's language
            if session_language == 'hi':
                return "Mujhe web search mein kuch dikkat ho rahi hai. Kripaya apna sawal dusre tarike se puchiye."
            elif session_language == 'ta':
                return "Enakku web search la konjam problem irukku. Ungaloda kelviya vera vidhamaaga kekka mudiyuma?"
            elif session_language == 'te':
                return "Naaku web search lo konni samasyalu unnaayi. Dayachesi mee prashnanu vere vidhamgaa adagandi."
            elif session_language == 'de':
                return "Ich habe ein Problem bei der Websuche. Bitte formulieren Sie Ihre Frage anders."
            elif session_language == 'fr':
                return "J'ai rencontré un problème lors de la recherche web. Veuillez reformuler votre question."
            else:
                return "I encountered an issue searching the web. Please try rephrasing your question."

    return web_search

def get_dynamic_language_instructions(lang_code: str, lang_config: dict) -> str:
    """
    Generate dynamic language instructions based on detected language.
    """
    if lang_code == 'en':
        return "\n**CURRENT SESSION LANGUAGE:** English - Respond in clear, natural English."

    lang_name = lang_config.get('name', 'Unknown')
    lang_type = language_detector.get_language_type(lang_code)

    if lang_type == 'indian':
        return f"""
**CURRENT SESSION LANGUAGE:** {lang_name} ({lang_code})
**MANDATORY RESPONSE FORMAT:** Use English alphabet transliteration that sounds natural when spoken in {lang_name}.
**EXAMPLE GREETING:** "{lang_config['sample_phrases']['greeting']}"
**EXAMPLE ERROR:** "{lang_config['sample_phrases']['error']}"
**CRITICAL:** Never use English words - always use {lang_name} equivalents in transliteration.
"""
    elif lang_type == 'european':
        return f"""
**CURRENT SESSION LANGUAGE:** {lang_name} ({lang_code})
**MANDATORY RESPONSE FORMAT:** Respond in proper {lang_name} with correct grammar and native vocabulary.
**EXAMPLE GREETING:** "{lang_config['sample_phrases']['greeting']}"
**EXAMPLE ERROR:** "{lang_config['sample_phrases']['error']}"
**CRITICAL:** Use authentic {lang_name} - no English mixing allowed.
"""
    else:
        return f"""
**CURRENT SESSION LANGUAGE:** {lang_name} ({lang_code})
**RESPONSE FORMAT:** Maintain natural pronunciation patterns in {lang_name}.
"""

class LanguageAwareAgent(Agent):
    """
    Enhanced Agent with ultra-strong language enforcement and parallel tool execution.
    """
    def __init__(self, llm, tools):
        # Start with default English instructions
        super().__init__(
            llm=llm,
            instructions=get_dynamic_instructions(),
            tools=tools
        )
        self.current_language = None
        self.language_config = None
        self.instructions_updated = False
        self.response_count = 0

    def update_language_instructions(self, lang_code: str, lang_config: dict):
        """Update agent instructions based on detected language."""
        if self.current_language != lang_code:
            self.current_language = lang_code
            self.language_config = lang_config

            # Generate new instructions for the detected language
            new_instructions = get_dynamic_instructions(lang_code, lang_config)

            # Update the agent's instructions
            self.instructions = new_instructions
            self.instructions_updated = True

            logging.info(f"Agent instructions updated for language: {lang_config.get('name', lang_code)} ({lang_code})")

    async def _handle_user_message(self, message):
        """Override to detect language from user messages and update instructions."""
        global session_language, session_language_config, session_language_instructions

        # Detect language from user message if not already detected
        if session_language is None and message.content:
            lang_code, lang_name, lang_config = language_detector.detect_language(message.content)
            session_language = lang_code
            session_language_config = lang_config
            session_language_instructions = language_detector.get_transliteration_instructions(lang_code)

            logging.info(f"Language detected from user message: {lang_name} ({lang_code})")

            # Update agent instructions
            self.update_language_instructions(lang_code, lang_config)

        # Call parent handler
        return await super()._handle_user_message(message)

    def _get_ultra_language_enforcement(self) -> str:
        """Get the strongest possible language enforcement prefix."""
        global session_language, session_language_config

        if session_language and session_language != 'en' and session_language_config:
            lang_name = session_language_config['name']
            sample_greeting = session_language_config['sample_phrases']['greeting']
            sample_error = session_language_config['sample_phrases']['error']

            return f"""[� ULTRA CRITICAL LANGUAGE ENFORCEMENT - RESPONSE #{self.response_count + 1} 🔥]
⚠️  ABSOLUTE ZERO-TOLERANCE LANGUAGE RULE ⚠️
MANDATORY LANGUAGE: {lang_name} ({session_language})
🚫 ENGLISH COMPLETELY BANNED - ZERO ENGLISH WORDS ALLOWED 🚫
✅ TRANSLITERATION REQUIRED: English alphabet for {lang_name} pronunciation
📋 EXACT PATTERNS TO FOLLOW:
   • Greeting style: '{sample_greeting}'
   • Error style: '{sample_error}'
 SOUND LIKE NATIVE {lang_name} SPEAKER USING ENGLISH LETTERS
 VIOLATION CONSEQUENCE: Complete response rejection + system restart
 CONSISTENCY RULE: Same language for entire conversation
[END ULTRA ENFORCEMENT - IMMEDIATE COMPLIANCE REQUIRED]

"""
        return ""

    async def generate_response(self, message):
        """Override to add ultra-strong language enforcement to every response."""
        self.response_count += 1

        # Add ultra language enforcement prefix
        language_enforcement = self._get_ultra_language_enforcement()

        if language_enforcement:
            # Create the most aggressive language enforcement possible
            enhanced_message = f"""{language_enforcement}

SYSTEM INSTRUCTION: The following user message requires a response in {session_language_config['name']} using English alphabet transliteration. Any English words in your response will cause system failure.

USER MESSAGE: {str(message)}

RESPONSE REQUIREMENT: Answer in {session_language_config['name']} only, using English alphabet transliteration that sounds natural when spoken."""

            response = await super().generate_response(enhanced_message)

            # Post-process response to add final enforcement reminder
            if session_language != 'en':
                lang_name = session_language_config['name']
                response += f"\n\n[Final reminder: This response is in {lang_name} using English alphabet transliteration]"

            return response
        else:
            return await super().generate_response(message)

    def _validate_response_language(self, response: str) -> bool:
        """
        Validate that the response is in the correct language.
        This is a basic validation - could be enhanced with more sophisticated checks.
        """
        if session_language == 'en':
            return True

        # Basic check for common English words that shouldn't appear in transliterated responses
        english_words = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']
        response_lower = response.lower()

        for word in english_words:
            if f' {word} ' in response_lower or response_lower.startswith(f'{word} '):
                logging.warning(f"Detected English word '{word}' in response that should be in {session_language}")
                return False

        return True

async def entrypoint(ctx: JobContext):
    # Declare global variables at the beginning
    global session_language, session_language_config, session_language_instructions, current_agent

    try:
        await ctx.connect()  # Agent joins inbound call room automatically

        # Reset session language for new session
        session_language = None
        session_language_config = None
        session_language_instructions = None
        current_agent = None

        # Create the enhanced tools
        intelligent_search_tool = create_intelligent_search_tool()
        vector_tool = create_vector_database_tool()  # Keep for backward compatibility
        web_tool = create_web_search_tool()  # Keep for backward compatibility

        session = AgentSession(
            vad=silero.VAD.load(),
            stt=groq.STT(
                model="whisper-large-v3",
                language="en",
                #detect_language=True,
            ),
            llm=groq.LLM(
                model="llama3-70b-8192",
            ),
            tts=speechify.TTS(
                model="simba-multilingual"
            ),
        )

        # Create enhanced language-aware agent with intelligent search
        agent = LanguageAwareAgent(
            llm=session.llm,
            tools=[intelligent_search_tool, vector_tool, web_tool]
        )

        # Set global reference for tools to access
        current_agent = agent

        await session.start(agent=agent, room=ctx.room)

        # Start with a multilingual greeting that will trigger language detection
        await session.generate_reply(instructions="Hello! Namaste! Vanakkam! How may I assist you today?")

    except Exception as e:
        logging.error(f"voice agent error: {e}")
        raise CustomException(e,sys)

if __name__ == "__main__":
    from livekit.agents import cli, WorkerOptions
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))
