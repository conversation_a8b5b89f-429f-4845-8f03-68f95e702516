import os
import sys
import asyncio
from typing import Dict, Any, Optional
from src.agent.agent import AgenticRA<PERSON>
from src.logging.logger import logging
from src.exception import CustomException
from src.utils.language_detection import LanguageDetector
from src.tools.vector_database_tool import VectorDatabaseTool
from src.tools.web_search_tool import WebSearchTool

from livekit.agents import AgentSession, Agent, JobContext, llm
from livekit.plugins import silero, groq, speechify
from dotenv import load_dotenv

load_dotenv()

# Global variables for session language tracking
session_language = None
session_language_config = None
rag_agent = AgenticRAG()
language_detector = LanguageDetector()

def get_language_specific_instructions(lang_code: str, lang_config: dict) -> str:
    """Generate language-specific instructions for the agent."""
    if lang_code == 'en':
        return """
You are a helpful voice AI assistant. Respond in clear, natural English.
Keep responses conversational and concise for voice interaction.
Use the available tools to provide accurate information.

TOOLS AVAILABLE:
- vector_database_search: For technical documentation and internal knowledge
- web_search: For current news and real-time information
"""

    lang_name = lang_config.get('name', 'Unknown')
    sample_greeting = lang_config.get('sample_phrases', {}).get('greeting', '')
    sample_error = lang_config.get('sample_phrases', {}).get('error', '')

    return f"""
You are a helpful multilingual voice AI assistant.

🚨🚨🚨 ABSOLUTE CRITICAL LANGUAGE RULE - ZERO TOLERANCE 🚨🚨🚨
- You MUST respond ONLY in {lang_name} - NEVER use English words
- Use English alphabet transliteration for {lang_name} words
- ANY English word usage will cause complete system failure
- Example greeting: '{sample_greeting}'
- Example error: '{sample_error}'

MANDATORY RESPONSE PATTERN:
- Detect user's language and respond in same language
- Use natural {lang_name} expressions with English alphabet
- Sound like a native {lang_name} speaker using English letters

TOOLS AVAILABLE:
- vector_database_search: For technical documentation and internal knowledge
- web_search: For current news and real-time information

CRITICAL SUCCESS CRITERIA:
- Every response MUST be in {lang_name} using English alphabet
- Zero English words allowed in responses
- Natural conversational tone for voice interaction
- Use tools when needed to provide accurate information
"""

def create_vector_database_tool():
    """Create a vector database search tool."""
    vector_tool = VectorDatabaseTool(rag_agent.llm)

    @llm.function_tool(
        name="vector_database_search",
        description="Search internal documents and knowledge base for technical information"
    )
    async def vector_database_search(query: str) -> str:
        """Search internal vector database for technical documentation."""
        try:
            global session_language, session_language_config
            logging.info(f"Vector database search: '{query}'")

            # Detect language if not set
            if session_language is None:
                lang_code, lang_name, lang_config = language_detector.detect_language(query)
                session_language = lang_code
                session_language_config = lang_config
                logging.info(f"Language detected: {lang_name} ({lang_code})")

            # Search vector database
            result = vector_tool.search_documents(query, session_language)
            
            if result['is_relevant']:
                response = result['results']
                logging.info("Vector database found relevant results")
            else:
                # No relevant results message in user's language
                if session_language == 'hi':
                    response = "Mere paas is sawal ka jawaab mere knowledge base mein nahi hai."
                elif session_language == 'ta':
                    response = "Enakku indha kelvikku ennoda knowledge base la information illa."
                elif session_language == 'te':
                    response = "Naa knowledge base lo ee prashnaku sambandhinchinavi levu."
                elif session_language == 'de':
                    response = "Ich habe keine relevanten Informationen in meiner Wissensdatenbank."
                elif session_language == 'fr':
                    response = "Je n'ai pas d'informations pertinentes dans ma base de connaissances."
                else:
                    response = "I don't have relevant information in my knowledge base for this query."
                logging.info("Vector database did not find relevant results")

            # Add strong language enforcement for non-English responses
            if session_language != 'en' and session_language_config:
                lang_name = session_language_config['name']
                sample_greeting = session_language_config['sample_phrases']['greeting']
                sample_error = session_language_config['sample_phrases']['error']
                response = f"""🚨 ABSOLUTE LANGUAGE ENFORCEMENT 🚨
MANDATORY RESPONSE LANGUAGE: {lang_name} ({session_language})
ZERO TOLERANCE RULE: You MUST respond ONLY in {lang_name} - NO English words allowed
TRANSLITERATION REQUIRED: Use English alphabet to write {lang_name} words
EXAMPLES TO FOLLOW EXACTLY:
- Greeting style: '{sample_greeting}'
- Error style: '{sample_error}'
CRITICAL: Any English word usage will result in complete response failure

CONTENT TO PROCESS:
{response}

FINAL REMINDER: Respond in {lang_name} using English alphabet transliteration ONLY"""

            return response

        except Exception as e:
            logging.error(f"Vector database tool error: {e}")
            return "I encountered an issue searching the knowledge base."

    return vector_database_search

def create_web_search_tool():
    """Create a web search tool."""
    web_tool = WebSearchTool()

    @llm.function_tool(
        name="web_search",
        description="Search the internet for current news and real-time information"
    )
    async def web_search(query: str) -> str:
        """Search the web for current information."""
        try:
            global session_language, session_language_config
            logging.info(f"Web search: '{query}'")

            # Detect language if not set
            if session_language is None:
                lang_code, lang_name, lang_config = language_detector.detect_language(query)
                session_language = lang_code
                session_language_config = lang_config
                logging.info(f"Language detected: {lang_name} ({lang_code})")

            # Perform web search
            result = web_tool.search_web(query, session_language)
            response = result['results']
            logging.info(f"Web search completed with {result['result_count']} results")

            # Add strong language enforcement for non-English responses
            if session_language != 'en' and session_language_config:
                lang_name = session_language_config['name']
                sample_greeting = session_language_config['sample_phrases']['greeting']
                sample_error = session_language_config['sample_phrases']['error']
                response = f"""🚨 ABSOLUTE LANGUAGE ENFORCEMENT 🚨
MANDATORY RESPONSE LANGUAGE: {lang_name} ({session_language})
ZERO TOLERANCE RULE: You MUST respond ONLY in {lang_name} - NO English words allowed
TRANSLITERATION REQUIRED: Use English alphabet to write {lang_name} words
EXAMPLES TO FOLLOW EXACTLY:
- Greeting style: '{sample_greeting}'
- Error style: '{sample_error}'
CRITICAL: Any English word usage will result in complete response failure

CONTENT TO PROCESS:
{response}

FINAL REMINDER: Respond in {lang_name} using English alphabet transliteration ONLY"""

            return response

        except Exception as e:
            logging.error(f"Web search tool error: {e}")
            # Return error message in user's language
            if session_language == 'hi':
                return "Mujhe web search mein kuch dikkat ho rahi hai."
            elif session_language == 'ta':
                return "Enakku web search la konjam problem irukku."
            elif session_language == 'te':
                return "Naaku web search lo konni samasyalu unnaayi."
            elif session_language == 'de':
                return "Ich habe ein Problem bei der Websuche."
            elif session_language == 'fr':
                return "J'ai rencontré un problème lors de la recherche web."
            else:
                return "I encountered an issue searching the web."

    return web_search

class LanguageAwareAgent(Agent):
    """Enhanced Agent with language detection and enforcement."""
    
    def __init__(self, llm, tools):
        super().__init__(
            llm=llm,
            instructions="You are a helpful voice AI assistant.",
            tools=tools
        )
        self.current_language = None
        self.language_config = None

    async def _handle_user_message(self, message):
        """Override to detect language and update instructions."""
        global session_language, session_language_config

        # Detect language from user message
        if session_language is None and message.content:
            lang_code, lang_name, lang_config = language_detector.detect_language(message.content)
            session_language = lang_code
            session_language_config = lang_config
            self.current_language = lang_code
            self.language_config = lang_config

            # Update agent instructions based on detected language
            self.instructions = get_language_specific_instructions(lang_code, lang_config)
            logging.info(f"Language detected: {lang_name} ({lang_code})")
            logging.info("Agent instructions updated for detected language")

        return await super()._handle_user_message(message)

    async def generate_response(self, message):
        """Override to add language enforcement to every response."""
        global session_language, session_language_config

        # If non-English language detected, add strong enforcement
        if session_language and session_language != 'en' and session_language_config:
            lang_name = session_language_config['name']
            sample_greeting = session_language_config['sample_phrases']['greeting']
            sample_error = session_language_config['sample_phrases']['error']

            # Create enhanced message with language enforcement
            enhanced_message = f"""🚨 CRITICAL LANGUAGE ENFORCEMENT 🚨
USER LANGUAGE DETECTED: {lang_name} ({session_language})
MANDATORY RESPONSE RULE: You MUST respond ONLY in {lang_name} using English alphabet
ZERO ENGLISH WORDS ALLOWED - Complete response failure if English is used
EXAMPLES TO FOLLOW:
- Greeting: '{sample_greeting}'
- Error: '{sample_error}'

USER MESSAGE: {str(message)}

RESPONSE REQUIREMENT: Answer in {lang_name} only, using English alphabet transliteration"""

            response = await super().generate_response(enhanced_message)

            # Add final reminder
            response += f"\n\n[Response delivered in {lang_name} using English alphabet]"
            return response
        else:
            return await super().generate_response(message)

async def entrypoint(ctx: JobContext):
    """Main entrypoint for the voice agent."""
    global session_language, session_language_config
    
    try:
        await ctx.connect()
        
        # Reset session language for new session
        session_language = None
        session_language_config = None
        
        # Create tools
        vector_tool = create_vector_database_tool()
        web_tool = create_web_search_tool()
        
        # Create session
        session = AgentSession(
            vad=silero.VAD.load(),
            stt=groq.STT(
                model="whisper-large-v3",
                language="en",
                detect_language=True,
            ),
            llm=groq.LLM(
                model="llama3-70b-8192",
            ),
            tts=speechify.TTS(
                model="simba-multilingual"
            ),
        )
        
        # Create language-aware agent
        agent = LanguageAwareAgent(
            llm=session.llm,
            tools=[vector_tool, web_tool]
        )
        
        await session.start(agent=agent, room=ctx.room)
        
        # Start with multilingual greeting to trigger language detection
        await session.generate_reply(
            instructions="""Greet the user in multiple languages to help them identify their preferred language. Say exactly:
'Hello! Namaste! Vanakkam! Namaskaram! Guten Tag! Bonjour! How may I assist you today?'
This will help detect their language preference for the conversation."""
        )
        
    except Exception as e:
        logging.error(f"Voice agent error: {e}")
        raise CustomException(e, sys)

if __name__ == "__main__":
    from livekit.agents import cli, WorkerOptions
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint))
